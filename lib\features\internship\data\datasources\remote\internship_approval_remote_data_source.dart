/// -----
/// internship_approval_remote_data_source.dart
///
/// 实习申请审批远程数据源抽象接口
/// 定义教师端实习申请审批相关的远程数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../data/models/internship_approval_response_model.dart';

/// 实习申请审批远程数据源抽象接口
///
/// 定义教师端实习申请审批相关的远程数据操作方法
abstract class InternshipApprovalRemoteDataSource {
  /// 获取实习申请列表
  ///
  /// 根据实习计划ID和申请类型获取实习申请列表
  ///
  /// 参数:
  ///   - [planId]: 实习计划ID
  ///   - [type]: 申请类型（0=待审批，1=已审批）
  ///
  /// 返回:
  ///   Future<InternshipApprovalResponseModel> 申请列表响应结果
  ///
  /// 异常:
  ///   - ServerException: 服务器错误
  ///   - NetworkException: 网络错误
  ///   - UnauthorizedException: 认证失败
  Future<InternshipApprovalResponseModel> getInternshipApprovalList({
    required String planId,
    required int type,
  });
}
