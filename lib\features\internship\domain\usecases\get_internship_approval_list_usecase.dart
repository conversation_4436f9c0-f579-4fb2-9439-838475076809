/// -----
/// get_internship_approval_list_usecase.dart
///
/// 获取实习申请审批列表用例
/// 封装获取教师端实习申请审批列表的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/internship_approval_repository.dart';
import '../../data/models/internship_application_model.dart';

/// 获取实习申请审批列表用例参数
class GetInternshipApprovalListParams extends Equatable {
  /// 实习计划ID
  final String planId;
  
  /// 申请类型（0=待审批，1=已审批）
  final int type;

  /// 构造函数
  const GetInternshipApprovalListParams({
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [planId, type];
}

/// 获取实习申请审批列表用例
///
/// 封装获取教师端实习申请审批列表的业务逻辑，包括数据验证和处理
class GetInternshipApprovalListUseCase implements UseCase<List<InternshipApplicationModel>, GetInternshipApprovalListParams> {
  /// 实习申请审批仓库
  final InternshipApprovalRepository repository;

  /// 构造函数
  ///
  /// 参数:
  ///   - [repository]: 实习申请审批仓库实例
  const GetInternshipApprovalListUseCase({
    required this.repository,
  });

  @override
  Future<Either<Failure, List<InternshipApplicationModel>>> call(GetInternshipApprovalListParams params) async {
    // 验证参数
    if (params.planId.isEmpty) {
      return const Left(ValidationFailure('实习计划ID不能为空'));
    }

    if (params.type < 0 || params.type > 1) {
      return const Left(ValidationFailure('申请类型参数无效'));
    }

    // 调用仓库获取数据
    return await repository.getInternshipApprovalList(
      planId: params.planId,
      type: params.type,
    );
  }
}
