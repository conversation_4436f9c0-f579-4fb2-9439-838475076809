/// -----
/// internship_approval_remote_data_source_impl.dart
///
/// 实习申请审批远程数据源具体实现
/// 使用DioClient进行网络请求，实现教师端实习申请审批相关的远程数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../../../core/network/dio_client.dart';
import '../../../../../core/error/exceptions/exceptions.dart';
import '../../../data/models/internship_approval_response_model.dart';
import 'internship_approval_remote_data_source.dart';

/// 实习申请审批远程数据源具体实现
///
/// 使用DioClient进行网络请求，实现教师端实习申请审批相关的远程数据操作
class InternshipApprovalRemoteDataSourceImpl implements InternshipApprovalRemoteDataSource {
  /// DIO网络客户端
  final DioClient dioClient;

  /// 构造函数
  ///
  /// 参数:
  ///   - [dioClient]: DIO网络客户端实例
  const InternshipApprovalRemoteDataSourceImpl({
    required this.dioClient,
  });

  @override
  Future<InternshipApprovalResponseModel> getInternshipApprovalList({
    required String planId,
    required int type,
  }) async {
    try {
      // 调用API接口
      final response = await dioClient.get(
        'internshipservice/v1/internship/apply/teacher/list',
        queryParameters: {
          'planId': planId,
          'type': type,
        },
      );

      // 解析响应数据
      // 注意：DioClient已经提取了data字段，所以response直接就是data的值
      if (response != null) {
        // 构造完整的响应模型
        // 由于DioClient已经验证了resultCode，这里可以安全地假设请求成功
        final responseData = {
          'data': response is List ? response : [],
          'resultCode': '0', // 成功状态码
          'resultMsg': 'success',
        };
        
        return InternshipApprovalResponseModel.fromJson(responseData);
      } else {
        throw ServerException('响应数据为空');
      }
    } on DioException catch (e) {
      // 处理网络异常
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.sendTimeout) {
        throw NetworkException('网络连接超时');
      } else if (e.type == DioExceptionType.connectionError) {
        throw NetworkException('网络连接失败');
      } else if (e.response?.statusCode == 401) {
        throw UnauthorizedException('认证失败，请重新登录');
      } else if (e.response?.statusCode == 403) {
        throw ForbiddenException('权限不足，无法访问该资源');
      } else {
        throw ServerException('获取实习申请列表失败: ${e.message}');
      }
    } on ServerException {
      // ServerException直接重新抛出
      rethrow;
    } on NetworkException {
      // NetworkException直接重新抛出
      rethrow;
    } on UnauthorizedException {
      // UnauthorizedException直接重新抛出
      rethrow;
    } catch (e) {
      // 其他异常转换为ServerException
      throw ServerException('获取实习申请列表失败: $e');
    }
  }
}
