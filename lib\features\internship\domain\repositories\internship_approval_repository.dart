/// -----
/// internship_approval_repository.dart
///
/// 实习申请审批仓库抽象接口
/// 定义教师端实习申请审批相关的数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../data/models/internship_application_model.dart';

/// 实习申请审批仓库抽象接口
///
/// 定义教师端实习申请审批相关的数据操作方法
/// 使用Either类型处理成功和失败的情况
abstract class InternshipApprovalRepository {
  /// 获取实习申请列表
  ///
  /// 根据实习计划ID和申请类型获取实习申请列表
  ///
  /// 参数:
  ///   - [planId]: 实习计划ID
  ///   - [type]: 申请类型（0=待审批，1=已审批）
  ///
  /// 返回:
  ///   Either<Failure, List<InternshipApplicationModel>> 成功返回申请列表，失败返回Failure
  Future<Either<Failure, List<InternshipApplicationModel>>> getInternshipApprovalList({
    required String planId,
    required int type,
  });
}
